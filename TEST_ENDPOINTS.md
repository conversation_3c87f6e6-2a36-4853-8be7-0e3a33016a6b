# Testing User Management Endpoints

## Prerequisites
1. Make sure your backend server is running
2. You have a valid JWT token from login
3. Replace `YOUR_JWT_TOKEN` with your actual token
4. Replace `http://localhost:3000` with your actual API base URL

## Test Commands

### 1. Test Debug Endpoint (Check if users exist)
```bash
curl -X GET "http://localhost:3000/api/users/debug" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 2. Test Basic Users Endpoint (No filters)
```bash
curl -X GET "http://localhost:3000/api/users" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 3. Test Users with Pagination
```bash
curl -X GET "http://localhost:3000/api/users?page=1&limit=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 4. Test Users with Search
```bash
curl -X GET "http://localhost:3000/api/users?searchTerm=admin" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 5. Test Users with Role Filter
```bash
curl -X GET "http://localhost:3000/api/users?roleFilter=SuperAdmin" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 6. Test Users with Status Filter
```bash
curl -X GET "http://localhost:3000/api/users?statusFilter=Active" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 7. Test Get Roles
```bash
curl -X GET "http://localhost:3000/api/users/roles" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 8. Test Get Departments
```bash
curl -X GET "http://localhost:3000/api/users/departments" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 9. Test Get User Statistics
```bash
curl -X GET "http://localhost:3000/api/users/stats" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 10. Test Get Status Options
```bash
curl -X GET "http://localhost:3000/api/users/status-options" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## Expected Responses

### Debug Endpoint Response
```json
{
  "success": true,
  "message": "Debug info retrieved",
  "data": {
    "totalUsers": 5,
    "sampleUser": {
      "userId": "uuid",
      "email": "<EMAIL>",
      "firstName": "Admin",
      "lastName": "User",
      "roleName": "SuperAdmin"
    },
    "allUsers": [...]
  }
}
```

### Users Endpoint Response
```json
{
  "success": true,
  "message": "Users retrieved successfully",
  "data": {
    "users": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    },
    "filters": {}
  }
}
```

## Troubleshooting

### If you get "No Users Found":
1. First test the debug endpoint to see if users exist in the database
2. Check the server logs for any error messages
3. Verify your JWT token is valid
4. Make sure the database connection is working

### If you get authentication errors:
1. Make sure you're including the Authorization header
2. Verify your JWT token is not expired
3. Check that the token format is correct: `Bearer YOUR_TOKEN`

### If you get 500 errors:
1. Check the server logs for detailed error messages
2. Verify the database connection
3. Make sure all required environment variables are set

## Server Logs to Watch

When testing, watch your server console for these log messages:
- `🎯 getUsers endpoint called`
- `🔍 getUsersWithFiltersAsync called with filters:`
- `📊 Total count: X`
- `👥 Found users: X`
- `✅ Service returned result with X users`

These logs will help identify where the issue is occurring.
