# User Management API Documentation

This document describes the enhanced user management endpoints that support the frontend user management interface.

## Base URL
All endpoints are prefixed with `/api`

## Authentication
All endpoints require JWT authentication via the `Authorization: Bearer <token>` header.

## Endpoints

### 1. Get Users with Advanced Filtering
**GET** `/api/users`

Retrieves users with advanced filtering, searching, sorting, and pagination capabilities.

#### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `searchTerm` (optional): Search across firstName, lastName, and email
- `roleFilter` (optional): Filter by role name (e.g., "SuperAdmin", "Student")
- `statusFilter` (optional): Filter by account status (e.g., "Active", "Inactive")
- `departmentFilter` (optional): Filter by department name
- `userTypeFilter` (optional): Filter by user type ("TSLS" or "University")
- `sortBy` (optional): Sort field ("firstName", "lastName", "email", "userCreatedAt", "accountStatus")
- `sortOrder` (optional): Sort direction ("asc" or "desc", default: "desc")

#### Example Request
```
GET /api/users?page=1&limit=10&searchTerm=john&roleFilter=Student&statusFilter=Active&sortBy=firstName&sortOrder=asc
```

#### Response
```json
{
  "success": true,
  "message": "Users retrieved successfully",
  "data": {
    "users": [
      {
        "userId": "uuid",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "roleId": "uuid",
        "roleName": "Student",
        "departmentId": "uuid",
        "departmentName": "Computer Science",
        "userType": "University",
        "accountStatus": "Active",
        "userCreatedAt": "2024-01-15T10:30:00Z",
        "userModifiedAt": "2024-01-15T10:30:00Z",
        "isAccountLockedOut": false,
        "failedLoginAttempts": 0,
        "userProfilePicPath": "ProfilePics/PTEIUniversityUserPic.png"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    },
    "filters": {
      "searchTerm": "john",
      "roleFilter": "Student",
      "statusFilter": "Active"
    }
  }
}
```

### 2. Get User Statistics
**GET** `/api/users/stats`

Retrieves user statistics for dashboard/analytics.

#### Response
```json
{
  "success": true,
  "message": "User statistics retrieved successfully",
  "data": {
    "totalUsers": 150,
    "activeUsers": 120,
    "inactiveUsers": 25,
    "lockedUsers": 5,
    "usersByRole": {
      "Student": 80,
      "Officer": 30,
      "University": 25,
      "SuperAdmin": 5
    },
    "usersByDepartment": {
      "Computer Science": 45,
      "Engineering": 35,
      "IT": 20,
      "Finance": 15
    },
    "usersByType": {
      "University": 100,
      "TSLS": 50
    },
    "recentUsers": 12
  }
}
```

### 3. Get All Roles
**GET** `/api/users/roles`

Retrieves all active roles for dropdown/filter options.

#### Response
```json
{
  "success": true,
  "message": "Roles retrieved successfully",
  "data": [
    {
      "roleId": "uuid",
      "roleName": "SuperAdmin",
      "description": "Full system access"
    },
    {
      "roleId": "uuid",
      "roleName": "Student",
      "description": "Student user"
    }
  ]
}
```

### 4. Get All Departments
**GET** `/api/users/departments`

Retrieves all active departments for dropdown/filter options.

#### Response
```json
{
  "success": true,
  "message": "Departments retrieved successfully",
  "data": [
    {
      "departmentId": "uuid",
      "departmentName": "IT",
      "description": "System management"
    },
    {
      "departmentId": "uuid",
      "departmentName": "Finance",
      "description": "Handles finance and billing"
    }
  ]
}
```

### 5. Get Status Options
**GET** `/api/users/status-options`

Retrieves available user status options.

#### Response
```json
{
  "success": true,
  "message": "Status options retrieved successfully",
  "data": ["Active", "Inactive", "Pending", "Suspended"]
}
```

### 6. Get User Types
**GET** `/api/users/user-types`

Retrieves available user types.

#### Response
```json
{
  "success": true,
  "message": "User types retrieved successfully",
  "data": ["TSLS", "University"]
}
```

### 7. Create User
**POST** `/api/users`

Creates a new user.

#### Request Body
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "New",
  "lastName": "User",
  "roleId": "uuid",
  "departmentId": "uuid",
  "userType": "TSLS"
}
```

### 8. Update User
**PUT** `/api/users/:id`

Updates an existing user.

#### Request Body
```json
{
  "firstName": "Updated",
  "lastName": "Name",
  "roleId": "uuid",
  "departmentId": "uuid"
}
```

### 9. Change User Status
**PATCH** `/api/users/:id/status`

Changes a user's status.

#### Request Body
```json
{
  "newStatus": "Inactive"
}
```

### 10. Delete User
**DELETE** `/api/users/:id`

Deletes a user.

#### Response
```json
{
  "success": true,
  "message": "User deleted successfully"
}
```

## Error Responses

All endpoints return error responses in the following format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Detailed error messages"] // Optional, for validation errors
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid/missing token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found (resource doesn't exist)
- `500`: Internal Server Error

## Frontend Integration

The frontend can use these endpoints to:

1. **Load initial data**: Use `/users/roles`, `/users/departments`, `/users/status-options`, and `/users/user-types` to populate dropdowns
2. **Display users**: Use `/users` with appropriate filters and pagination
3. **Search and filter**: Pass search terms and filters as query parameters to `/users`
4. **Show statistics**: Use `/users/stats` for dashboard widgets
5. **CRUD operations**: Use POST, PUT, PATCH, and DELETE endpoints for user management

## Notes

- All date fields are returned in ISO 8601 format
- UUIDs are used for all ID fields
- Pagination is 1-based (first page is page 1)
- Search is case-insensitive and searches across firstName, lastName, and email fields
- Filters are exact matches (case-sensitive for role/department names)
