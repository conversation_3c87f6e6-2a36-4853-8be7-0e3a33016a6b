// Quick debug script to check users in database
import { PrismaClient } from './src/generated/prisma/index.js';

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    console.log('🔍 Checking users in database...');
    
    // Connect to database
    await prisma.$connect();
    console.log('✅ Database connected');

    // Count total users
    const totalUsers = await prisma.pTEIUser.count();
    console.log('📊 Total users:', totalUsers);

    if (totalUsers === 0) {
      console.log('❌ No users found in database!');
      console.log('💡 You may need to seed the database or create users first');
      return;
    }

    // Get first few users
    const users = await prisma.pTEIUser.findMany({
      take: 3,
      include: {
        Role: true,
        Department: true
      }
    });

    console.log('👥 Sample users:');
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.FirstName} ${user.LastName}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Role: ${user.Role?.RoleName || 'No Role'}`);
      console.log(`   Status: ${user.AccountStatus}`);
      console.log(`   Type: ${user.UserType}`);
      console.log('');
    });

    // Test the exact query that the API uses
    console.log('🧪 Testing API query...');
    const apiUsers = await prisma.pTEIUser.findMany({
      where: {},
      include: {
        Role: true,
        Department: true
      },
      orderBy: { UserCreatedAt: 'desc' },
      take: 10
    });

    console.log('📋 API query returned:', apiUsers.length, 'users');

  } catch (error) {
    console.error('💥 Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
